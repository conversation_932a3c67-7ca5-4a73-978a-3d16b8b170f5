package com.siteweb.powerdistribution.domain.document;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siteweb.powerdistribution.domain.AnchorBinding;
import com.siteweb.powerdistribution.domain.IObject;
import com.siteweb.powerdistribution.domain.enums.ObjectType;
import lombok.Data;

import java.util.UUID;

@Data
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, getterVisibility = JsonAutoDetect.Visibility.NONE)
public class AnchorInfomation implements IObject {

    @JsonProperty("Id")
    public UUID Id;


    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @JsonProperty("Type")
    public ObjectType Type;

    @JsonProperty("Position")
    public Point Position;

    @JsonProperty("Binding")
    public String Binding;

    @JsonProperty("ParentID")
    public UUID ParentID;

    @JsonProperty("ZIndex")
    public Integer ZIndex;

    @JsonProperty("HandleEvent")
    public Boolean HandleEvent;

    @JsonProperty("Connect")
    public AnchorBinding Connect;

    /// <summary>
    /// 绑定的设备
    /// </summary>
    @JsonProperty("BindDevice")
    public Integer BindDevice;

    /// <summary>
    /// 绑定的CorePoint
    /// </summary>
    @JsonProperty("StatusPoint")
    public Long StatusPoint;

    /// <summary>
    /// 状态条件
    /// </summary>
    @JsonProperty("StatusCondition")
    public StatusCondition StatusCondition;


}
