package com.siteweb.powerdistribution.business;

import com.siteweb.powerdistribution.business.nodes.SegmentNode;
import com.siteweb.powerdistribution.domain.enums.AnchorMode;
import com.siteweb.powerdistribution.domain.enums.PowerSupplyStatus;
import com.siteweb.prealarm.entity.PreAlarm;
import lombok.Data;

import java.util.List;
import java.util.UUID;

@Data
public class SegmentPath {

    //线路对象列表
    private List<SegmentNode> segments;
    //线的开始锚点
    private AnchorNode source;
    //目标锚点对象
    private AnchorNode target;
    //锚点所连接到的对象
    private NodeObject connectTarget;


    public SegmentPath(AnchorNode sourceAnchor, AnchorNode targetAnchor, List<SegmentNode> path) {
        source = sourceAnchor;
        target = targetAnchor;
        segments = path;
    }

    public List<SegmentNode> getPath() {
        return segments;
    }

    public AnchorMode getMode() {
        return target.getConnect().Mode;
    }


    /**
     * @param powerSupplyStatus 线路供应状态
     * @param sourceStatus      当前节点的状态
     * @param targetStatus      目标节点的状态
     * @Description: 设置当前线路的状态
     * @Return: void
     * @Author: admin
     * @Date: 2019/11/9
     **/
    public void setStatus(PowerSupplyStatus powerSupplyStatus, Boolean sourceStatus, Boolean targetStatus) {

        if (this.getConnectTarget() != null && sourceStatus) {
            //设置线路末端设备的供电状态
            this.getConnectTarget().setStatus(powerSupplyStatus, true, targetStatus);
        }
        for (SegmentNode segment : segments) {
            //设置线路的供电状态
            segment.setStatus(powerSupplyStatus, sourceStatus, targetStatus);
        }

        //设置Node 供电状态


    }

    public void resetStatus() {
        for (SegmentNode segment : segments) {
            //设置线路的供电状态
            segment.resetStatus();
        }
    }

    public void buildStyle() {
        for (SegmentNode segment : segments) {
            //设置线路的供电状态
            segment.buildStyle();
        }
    }

    public void uploadPreAlarm(PreAlarm maxPreAlarm){
        for (SegmentNode segment : segments) {
            segment.maxPreAlarm = maxPreAlarm;
            segment.getContext().getStyleBuilder().BuildCapacityStyle(segment, segment.property);
        }
    }


    public UUID getTargetId() {
        return target.getConnect().Target;
    }

    public AnchorNode getTarget() {
        return target;
    }
}
