package com.siteweb.powerdistribution.services.impl;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.powerdistribution.business.CableHub;
import com.siteweb.powerdistribution.business.DiagramRunner;
import com.siteweb.powerdistribution.business.NodeObject;
import com.siteweb.powerdistribution.business.nodes.ComponentNode;
import com.siteweb.powerdistribution.common.RunnerContext;
import com.siteweb.powerdistribution.domain.IObject;
import com.siteweb.powerdistribution.domain.document.ComponentInfomation;
import com.siteweb.powerdistribution.domain.document.DesignerDocument;
import com.siteweb.powerdistribution.dto.EquipmentInfo;
import com.siteweb.powerdistribution.dto.EquipmentLevelDTO;
import com.siteweb.powerdistribution.services.APPInterfaceService;
import com.siteweb.powerdistribution.services.DataProviderService;
import com.siteweb.powerdistribution.services.DistributionFileService;
import com.siteweb.powerdistribution.startup.PowerDistributionDiagramManager;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class APPInterfaceServiceImpl implements APPInterfaceService {
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired //
    private PowerDistributionDiagramManager diagramManager; // 注入配电图管理器
    @Autowired
    private DataProviderService dataProvider; // 注入数据提供服务
    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    public DistributionFileService distributionFileService;

    @Autowired
    public ComputerRackService computerRackService;


    @Autowired
    AccountService accountService;

    @Autowired
    RedisTemplate<String, String> redisTemplate;

    @Autowired
    TokenUtil tokenUtil;

    private static final String LOGIN_USER_TOKEN = "LoginUserToken";

    private static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }


    @Override
    public EquipmentLevelDTO getEquipmentLevelById(int equipmentId) {
        EquipmentLevelDTO res = new EquipmentLevelDTO();
        try {
            DiagramRunner resRunner = getDiagramRunnerByEquipmentId(equipmentId);
            if (resRunner == null)
                return null;
            List<NodeObject> entryList = resRunner.getEntrys();
            for (NodeObject oneObject : entryList) {
                ComponentNode componentNode = (ComponentNode) oneObject;
                if (componentNode != null)
                    getEquipmentLevelInfo(componentNode, equipmentId, res);
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getEquipmentLevelById error", e);
            return null;
        }
        return res;
    }

    @Override
    public EquipmentLevelDTO getEquipmentChildList(Integer equipmentId) {
        EquipmentLevelDTO res = new EquipmentLevelDTO();
        try {
            DiagramRunner resRunner = getDiagramRunnerByEquipmentId(equipmentId);
            if (resRunner == null)
                return null;
            List<NodeObject> entryList = resRunner.getEntrys();
            for (NodeObject oneObject : entryList) {
                ComponentNode componentNode = (ComponentNode) oneObject;
                if (componentNode != null)
                    getEquipmentChildList(componentNode, equipmentId, res);
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getEquipmentLevelById error", e);
            return null;
        }
        return res;
    }

    private String getTokenByUserName(String useId) {
        List<AccountDTO> accounts = accountService.findByLogonId(useId);
        AccountDTO accountDTO = accounts.get(0);
        String loginUserKey = accountDTO.getLogonId() + ":" + "web";
        Object objOldToken = redisTemplate.opsForHash().get(LOGIN_USER_TOKEN, loginUserKey);
        String newToken = String.valueOf(objOldToken);
        if (ObjectUtil.isNull(objOldToken)) {
            TokenUser tokenUser = new TokenUser(accountDTO);
            newToken = tokenUtil.createTokenForUser(tokenUser, "web");
        }
        return newToken;
    }


    @Override
    @Retryable(
            value = {Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 500)
    )
    public String getPowerDistributionInfo(Integer equipmentId, Integer computerRackId, Boolean isEquipment) {
        Authentication authentication = getAuthentication();
        String userName = "admin";
        if (authentication != null) {
            userName = (String) authentication.getPrincipal();
        }
        String tokenByUserName = getTokenByUserName(userName);
        SystemConfig systemConfig = systemConfigService.findBySystemConfigKey("pd.jiangxidianxin.chrome");
        if (systemConfig == null || !systemConfig.getSystemConfigValue().equals("true")) {
           throw new RuntimeException("未开启配电图截图功能");
        }
        try {
            DiagramRunner diagramRunner = null;
            if (isEquipment) {
                diagramRunner = getDiagramRunnerByEquipmentId(equipmentId);
            } else {
                /*根据机架ID获取层级ID*/
                ComputerRack computerRack = computerRackService.findComputerRack(computerRackId);
                if (computerRack == null) {
                    throw new Exception("未找到机架");
                }
                diagramRunner = getDiagramRunnerByResourceStructureId(computerRack.getResourceStructureId());
            }
            if (diagramRunner != null) {
                RunnerContext context = diagramRunner.getContext();
                if (context != null) {
                    Integer diagramId = context.getDiagramId();
                    return distributionFileService.getDiagramImage(diagramId, tokenByUserName);
                }
            } else {
                throw new Exception("未找到配电图");
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getPowerDistributionInfo error", e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }

    public DiagramRunner getDiagramRunnerByEquipmentId(Integer equipmentId) {
        DiagramRunner resRunner = null;
        try {
            List<DiagramRunner> diagramRunners = diagramManager.getDiagrams().values().stream().toList();
            for (DiagramRunner oneDiagramRunner : diagramRunners) {
                RunnerContext context = oneDiagramRunner.getContext();
                if (context == null)
                    continue;
                DesignerDocument document = context.getDocument();
                if (document == null)
                    continue;
                List<IObject> components = document.Components;
                if (components == null || components.isEmpty())
                    continue;
                for (IObject oneIObject : components) {
                    if (oneIObject instanceof ComponentInfomation) {
                        Integer oneEquipmentId = ((ComponentInfomation) oneIObject).getBindDevice();
                        if (oneEquipmentId == null)
                            continue;
                        if (oneEquipmentId.equals(equipmentId)) {
                            resRunner = oneDiagramRunner;
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getDiagramRunnerByEquipmentId error", e);
            return null;
        }
        return resRunner;
    }

    public DiagramRunner getDiagramRunnerByResourceStructureId(Integer resourceStructureId) {
        DiagramRunner resRunner = null;
        try {
            List<DiagramRunner> diagramRunners = diagramManager.getDiagrams().values().stream().toList();
            for (DiagramRunner oneDiagramRunner : diagramRunners) {
                RunnerContext context = oneDiagramRunner.getContext();
                if (context == null)
                    continue;
                DesignerDocument document = context.getDocument();
                if (document == null)
                    continue;
                Integer resId = document.getResourceStructureId();
                if (resId != null && resId.equals(resourceStructureId)) {
                    resRunner = oneDiagramRunner;
                    break;
                }
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getDiagramRunnerByResourceStructureId error", e);
            return null;
        }
        return resRunner;
    }

    public void getEquipmentLevelInfo(ComponentNode componentNode, Integer equipmentId, EquipmentLevelDTO res) {
        try {
            Integer curEquipmentId = componentNode.getBindDevice();
            //当前设备ID与查找的设备ID相同
            if (curEquipmentId != null && curEquipmentId.equals(equipmentId)) {
                res.setEquipmentId(curEquipmentId);
                String equipmentName = "";
                Equipment equipment = dataProvider.findEquipment(curEquipmentId);
                String equipmentPosition = "";
                if (equipment != null) {
                    equipmentPosition = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
                    equipmentName = equipment.getEquipmentName();
                }
                //往结果里写入设备信息
                res.setEquipmentName(equipmentName);
                res.setEquipmentPosition(equipmentPosition);
                //寻找此节点的下一节点寻找其子设备
                List<CableHub> outPuts = componentNode.getOutPuts();
                if (outPuts != null && !outPuts.isEmpty()) {
                    for (CableHub oneCab : outPuts) {
                        List<ComponentNode> connectTarget = findComponentNodeByCaleHub(oneCab);
                        for (ComponentNode oneNode : connectTarget) {
                            if (oneNode != null) {
                                EquipmentInfo temp = new EquipmentInfo();
                                Integer tempId = oneNode.getBindDevice();
                                if (tempId != null) {
                                    temp.setEquipmentId(tempId);
                                    String tempName = "";
                                    Equipment tempEquipment = dataProvider.findEquipment(tempId);
                                    String tempEquipmentPosition = "";
                                    if (tempEquipment != null) {
                                        tempName = tempEquipment.getEquipmentName();
                                        tempEquipmentPosition = resourceStructureManager.getFullPath(tempEquipment.getResourceStructureId());
                                    }
                                    temp.setEquipmentPosition(tempEquipmentPosition);
                                    temp.setEquipmentName(tempName);
                                }
                                if (temp.getEquipmentId() != null || temp.getEquipmentName() != null)
                                    res.addChild(temp);
                            }
                        }
                    }
                }
            }

            List<CableHub> outPuts = componentNode.getOutPuts();
            if (outPuts != null && !outPuts.isEmpty()) {
                for (CableHub oneCab : outPuts) {
                    List<ComponentNode> connectTarget = findComponentNodeByCaleHub(oneCab);
                    for (ComponentNode oneNode : connectTarget) {
                        if (oneNode != null) {
                            EquipmentInfo temp = new EquipmentInfo();
                            Integer tempId = oneNode.getBindDevice();
                            //如果当前节点的某子节点的设备ID与传入的设备ID相等，则此节点是查找设备节点的父节点
                            if (tempId != null && tempId.equals(equipmentId)) {
                                int parentId = componentNode.getBindDevice();
                                temp.setEquipmentId(parentId);
                                String tempName = "";
                                String tempEquipmentPosition = "";
                                Equipment tempEquipment = dataProvider.findEquipment(parentId);
                                if (tempEquipment != null) {
                                    tempName = tempEquipment.getEquipmentName();
                                    tempEquipmentPosition = resourceStructureManager.getFullPath(tempEquipment.getResourceStructureId());
                                }
                                temp.setEquipmentName(tempName);
                                temp.setEquipmentPosition(tempEquipmentPosition);
                                res.addParent(temp);
                            }
                            getEquipmentLevelInfo(oneNode, equipmentId, res);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getEquipmentLevelInfo error", e);
        }
    }

    public void getEquipmentChildList(ComponentNode componentNode, Integer equipmentId, EquipmentLevelDTO res) {
        try {
            Integer curEquipmentId = componentNode.getBindDevice();
            //当前设备ID与查找的设备ID相同
            if (curEquipmentId != null && curEquipmentId.equals(equipmentId)) {
                res.setEquipmentId(curEquipmentId);
                String equipmentName = "";
                Equipment equipment = dataProvider.findEquipment(curEquipmentId);
                String equipmentPosition = "";
                if (equipment != null) {
                    equipmentPosition = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
                    equipmentName = equipment.getEquipmentName();
                }
                //往结果里写入设备信息
                res.setEquipmentName(equipmentName);
                res.setEquipmentPosition(equipmentPosition);
                getChild(componentNode, res);
            }else{
                List<CableHub> outPuts = componentNode.getOutPuts();
                if (outPuts != null && !outPuts.isEmpty()) {
                    for (CableHub oneCab : outPuts) {
                        List<ComponentNode> connectTarget = findComponentNodeByCaleHub(oneCab);
                        for (ComponentNode oneNode : connectTarget) {
                            if (oneNode != null) {
                                getEquipmentChildList(oneNode, equipmentId, res);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getEquipmentLevelInfo error", e);
        }
    }

    //寻找此节点的下一节点寻找其子设备
    private void getChild(ComponentNode componentNode, EquipmentLevelDTO res) {
        try {
            List<CableHub> outPuts = componentNode.getOutPuts();
            if (outPuts != null && !outPuts.isEmpty()) {
                for (CableHub oneCab : outPuts) {
                    List<ComponentNode> connectTarget = findComponentNodeByCaleHub(oneCab);
                    for (ComponentNode oneNode : connectTarget) {
                        getChild(oneNode, res);
                        if (oneNode != null) {
                            EquipmentInfo temp = new EquipmentInfo();
                            Integer tempId = oneNode.getBindDevice();
                            if (tempId != null) {
                                temp.setEquipmentId(tempId);
                                String tempName = "";
                                Equipment tempEquipment = dataProvider.findEquipment(tempId);
                                String tempEquipmentPosition = "";
                                if (tempEquipment != null) {
                                    tempName = tempEquipment.getEquipmentName();
                                    tempEquipmentPosition = resourceStructureManager.getFullPath(tempEquipment.getResourceStructureId());
                                }
                                temp.setEquipmentPosition(tempEquipmentPosition);
                                temp.setEquipmentName(tempName);
                            }
                            if (temp.getEquipmentId() != null || temp.getEquipmentName() != null)
                                res.addChild(temp);

                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("---APPInterfaceServiceImpl---getEquipmentChildList error", e);
        }
    }



    // 通过电缆接头找到相关联的组件节点
    private List<ComponentNode> findComponentNodeByCaleHub(CableHub cableHub) {
        // 初始化结果列表
        List<ComponentNode> res = new ArrayList<>();
        // 如果电缆接头有目标设备
        if (!cableHub.getTargets().isEmpty()) {
            // 遍历电缆接头的所有目标设备
            cableHub.getTargets().forEach((uuid, segmentPath) -> {
                // 获取连接目标设备的组件节点
                ComponentNode connectTarget = (ComponentNode) segmentPath.getConnectTarget();
                // 如果连接目标设备的组件节点存在
                if (ObjectUtil.isNotNull(connectTarget)) {
                    // 将连接目标设备的组件节点添加到结果列表中
                    res.add(connectTarget);
                }
            });
            // 返回结果列表
            return res;
        }
        // 如果电缆接头没有目标设备，则返回一个空的ArrayList
        return new ArrayList<>();
    }
}
