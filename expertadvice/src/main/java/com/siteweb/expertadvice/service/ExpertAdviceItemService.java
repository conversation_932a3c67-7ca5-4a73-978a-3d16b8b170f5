package com.siteweb.expertadvice.service;

import com.siteweb.expertadvice.entity.ExpertAdviceItem;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lzy
 * @Date: 2023/5/22 17:22
 */
public interface ExpertAdviceItemService {
    List<ExpertAdviceItem> getExpertAdvice();

    void saveExpertAdvice(ExpertAdviceItem ExpertAdviceItem);

    void updateExpertAdvice(ExpertAdviceItem ExpertAdviceItem);

    void deleteExpertAdvice(Collection<String> split);

    List<ExpertAdviceItem> getExpertAdviceByBaseAlarmId(Integer baseAlarmId);

    boolean existsById(Integer baseAlarmId);
}
