spring:
  datasource:
    #   #刷脚本的时候添加preferQueryMode=simple参数,刷完在去除
    url: *****************************************************************************************************************************************************************************************************************************************************************************
    username: postgres
    password: Vertiv@086
    driver-class-name: org.opengauss.Driver
  liquibase:
    change-log: classpath:db/changelog/siteweb/opengauss/db.opengauss.changelog-master.xml
    enabled: false
  quartz:
    properties:
      org:
        quartz:
          jobStore:
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
mybatis-plus:
  configuration:
    database-id: opengauss
  mapper-locations: classpath*:mapper/postgres/**/*Mapper.xml
  # 用于注册自定义类型转换器
  type-handlers-package: com.siteweb.common.pgtypehandler