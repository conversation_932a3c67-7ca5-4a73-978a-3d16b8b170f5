package com.siteweb.hmi.manager;

import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.hmi.job.ComtradeFileJob;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.utility.service.SystemConfigService;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.quartz.model.SchedulerJob;
import com.siteweb.utility.quartz.model.TriggerModel;
import com.siteweb.utility.quartz.service.SchedulerJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.Map;
import java.util.stream.Collectors;
    
/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/22
 */
@Slf4j
@Component
public class ComtradeFileManager implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {
    private final CopyOnWriteArrayList<EquipmentDTO> equipmentList = new CopyOnWriteArrayList<>();
    private final Map<String, String> signalCache = new ConcurrentHashMap<>();
    
    // 添加初始化完成标志
    private final AtomicBoolean initializationCompleted = new AtomicBoolean(false);
    
    // 定义占位符常量，用于替代null值
    private static final String NULL_VALUE_PLACEHOLDER = "__COMTRADE_NULL_PLACEHOLDER__";

    // COMTRADE定时任务配置常量
    public static final String COMTRADE_SCHEDULED_INTERVAL_SECONDS = "comtrade.scheduled.interval.seconds";
    public static final int DEFAULT_SCHEDULED_INTERVAL_SECONDS = 300;

    @Autowired
    private ActiveSignalManager activeSignalManager;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private ComtradeFtpService comtradeFtpService;

    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;

    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private ConfigSignalManager configSignalManager;

    @Autowired
    private SchedulerJobService schedulerJobService;

    /**
     * 获取设备列表
     * @return 设备列表
     */
    public CopyOnWriteArrayList<EquipmentDTO> getEquipmentList() {
        return equipmentList;
    }

    /**
     * 获取信号缓存
     * @return 信号缓存
     */
    public Map<String, String> getSignalCache() {
        return signalCache;
    }

    /**
     * 检查COMTRADE功能是否启用
     * @return true表示功能启用，false表示功能未启用
     */
    private boolean isComtradeEnabled() {
        try {
            var config = systemConfigService.findBySystemConfigKey("comtrade.enable");
            if (config == null || config.getSystemConfigValue() == null) {
                log.debug("未找到COMTRADE启用配置");
                return false;
            }
            
            String comtradeEnable = config.getSystemConfigValue();
            return comtradeEnable != null && !comtradeEnable.trim().isEmpty() && "true".equalsIgnoreCase(comtradeEnable);
        } catch (Exception e) {
            log.debug("获取COMTRADE启用状态失败，默认不启用", e);
            return false;
        }
    }

    /**
     * 从系统配置中获取baseTypeIds
     * @return baseTypeIds列表
     */
    private List<Long> getComtradeSignalBaseTypeIds() {
        try {
            var config = systemConfigService.findBySystemConfigKey("comtrade.signal.basetypeids");
            if (config == null || config.getSystemConfigValue() == null) {
                log.warn("未找到COMTRADE信号基类ID配置或配置值为空，使用默认值");
                return Arrays.asList(1L, 2L, 3L);
            }
            
            String baseTypeIdsConfig = config.getSystemConfigValue();
            if (StringUtils.hasText(baseTypeIdsConfig)) {
                return Arrays.stream(baseTypeIdsConfig.split(","))
                        .map(String::trim)
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("从系统配置获取COMTRADE信号基类ID失败，使用默认值", e);
        }
        // 如果获取失败，返回默认值
        return Arrays.asList(1L, 2L, 3L);
    }

    /**
     * 设置初始化完成状态
     * 由ComtradeFTPRunner调用，表示初始化任务已完成
     */
    public void setInitializationCompleted() {
        initializationCompleted.set(true);
        log.info("ComtradeFileManager 初始化完成标志已设置");
    }
    
    /**
     * 检查初始化是否完成
     * @return true表示初始化已完成
     */
    public boolean isInitializationCompleted() {
        return initializationCompleted.get();
    }

    // 初始化调用
    @PostConstruct
    public synchronized void init() {
        // 检查COMTRADE功能是否启用
        if (!isComtradeEnabled()) {
            log.info("COMTRADE功能未启用，跳过初始化");
            return;
        }

        if (!haStatusService.isMasterHost()) {
            log.info("ComtradeFileManager not started, HA not enabled or not master host");
            return;
        }
        
        // 通过信号基类ID获取设备列表
        List<Long> signalBaseTypeIds = getComtradeSignalBaseTypeIds();
        log.info("获取到COMTRADE信号基类ID: {}", signalBaseTypeIds);
        
        // 获取匹配的信号配置
        List<ConfigSignalItem> configSignals = configSignalManager.getConfigSignalsByBaseTypeIds(signalBaseTypeIds);
        log.info("获取到匹配的信号配置数量: {}", configSignals.size());
        
        // 从信号配置中提取设备模板ID
        List<Integer> equipmentTemplateIds = configSignals.stream()
                .map(ConfigSignalItem::getEquipmentTemplateId)
                .distinct()
                .collect(Collectors.toList());
        log.info("获取到设备模板ID集合: {}", equipmentTemplateIds);
        
        // 通过设备模板ID获取设备列表
        List<EquipmentDTO> equipments = equipmentService.findEquipmentsByEquipmentTemplateIds(equipmentTemplateIds);
        
        equipmentList.clear();
        equipmentList.addAll(equipments);
        log.info("初始化设备列表完成，共 {} 个设备", equipmentList.size());

        // 初始化 signalCache
        signalCache.clear(); // Clear any existing cache data
        log.info("开始初始化信号缓存...");
        for (EquipmentDTO equipment : equipmentList) {
            try {
                List<Long> baseTypeIds = getComtradeSignalBaseTypeIds();
                List<SimpleActiveSignal> activeSignals = 
                        activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipment.getEqId(), baseTypeIds);

                if (activeSignals != null && !activeSignals.isEmpty()) {
                    for (SimpleActiveSignal signal : activeSignals) {
                        String cacheKey = equipment.getEqId() + "_" + signal.getSignalId();
                        String currentValue = signal.getCurrentValue();
                        // 如果当前值为null，使用占位符替代
                        signalCache.put(cacheKey, currentValue != null ? currentValue : NULL_VALUE_PLACEHOLDER);
                    }
                    log.info("设备ID: {} 的 {} 个活动信号已加入缓存", equipment.getEqId(), activeSignals.size());
                } else {
                    log.info("未找到设备ID: {} 的活动信号进行缓存初始化", equipment.getEqId());
                }
            } catch (Exception e) {
                log.error("初始化设备ID: " + equipment.getEqId() + " 的信号缓存时出错", e);
            }
        }
        log.info("信号缓存初始化完成，共 {} 个信号", signalCache.size());

        // 初始化Quartz定时任务
        initializeQuartzScheduler();
    }

    /**
     * 初始化Quartz调度器
     */
    private void initializeQuartzScheduler() {
        try {
            // 获取定时任务间隔配置
            var intervalConfig = systemConfigService.findBySystemConfigKey(COMTRADE_SCHEDULED_INTERVAL_SECONDS);
            int intervalSeconds = DEFAULT_SCHEDULED_INTERVAL_SECONDS;
            if (intervalConfig != null && intervalConfig.getSystemConfigValue() != null) {
                try {
                    intervalSeconds = Integer.parseInt(intervalConfig.getSystemConfigValue());
                } catch (NumberFormatException e) {
                    log.warn("无法解析COMTRADE定时任务间隔配置，使用默认值: {}", DEFAULT_SCHEDULED_INTERVAL_SECONDS);
                }
            }

            // 创建调度任务
            SchedulerJob schedulerJob = new SchedulerJob();
            schedulerJob.setClassName(ComtradeFileJob.class.getName());
            schedulerJob.setRepeatInterval((long) intervalSeconds * 1000); // 转换为毫秒
            schedulerJob.setJobGroup("ComtradeFileJob");
            schedulerJob.setJobName("ComtradeFileJob");
            schedulerJob.setTriggerType(TriggerModel.SIMPLE_TRIGGER_TYPE);
            schedulerJob.setParams(new HashMap<>());

            // 先移除可能存在的旧任务，然后添加新任务
            schedulerJobService.removeSchedulerJob(schedulerJob);
            schedulerJobService.addSchedulerJob(schedulerJob);
            
            log.info("ComtradeFileJob Quartz定时任务初始化完成，执行间隔: {} 秒", intervalSeconds);
        } catch (Exception e) {
            log.error("初始化ComtradeFileJob Quartz定时任务失败", e);
        }
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<HAStatusChanged> event) {
        //如果未启用双机高可用部署，则直接退出
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = event.getData();
        log.info("{} receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", 
                getClass().getSimpleName(), haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        init();
    }
}
