package com.siteweb.hmi.service;

import java.util.List;

/**
 * COMTRADE文件FTP服务接口
 */
public interface ComtradeFtpService {
    
    /**
     * 通过设备ID获取IP地址
     * @param equipmentId 设备ID
     * @return IP地址
     */
    String getIpAddressByEquipmentId(Integer equipmentId);
    
    /**
     * 下载指定设备的所有COMTRADE文件
     * @param ipAddress 设备IP地址
     * @param equipmentId 设备ID
     * @return 下载文件数量
     */
    int downloadComtradeFiles(String ipAddress, Integer equipmentId);
    
    /**
     * 下载最新COMTRADE文件
     * @param ipAddress 设备IP地址
     * @param equipmentId 设备ID
     * @return 下载文件数量
     */
    int downloadLatestComtradeFiles(String ipAddress, Integer equipmentId);

    /**
     * 下载最新COMTRADE文件（带超时控制）
     * @param ipAddress 设备IP地址
     * @param equipmentId 设备ID
     * @param timeoutMinutes 超时时间（分钟）
     * @return 下载文件数量
     */
    int downloadLatestComtradeFilesWithTimeout(String ipAddress, Integer equipmentId, int timeoutMinutes);

    /**
     * 获取设备的所有COMTRADE文件列表
     * @param equipmentId 设备ID
     * @return 文件列表
     */
    List<String> getComtradeFileList(Integer equipmentId);
} 