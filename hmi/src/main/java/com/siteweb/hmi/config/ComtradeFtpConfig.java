package com.siteweb.hmi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * COMTRADE FTP下载配置
 * 
 * <AUTHOR>
 * @since 2025/7/31
 */
@Data
@Component
@ConfigurationProperties(prefix = "comtrade.ftp")
public class ComtradeFtpConfig {
    
    /**
     * 连接超时时间（毫秒），默认30秒
     */
    private int connectTimeoutMs = 30000;
    
    /**
     * Socket超时时间（毫秒），默认5分钟
     */
    private int socketTimeoutMs = 300000;
    
    /**
     * 数据传输超时时间（分钟），默认60分钟
     */
    private int dataTimeoutMinutes = 60;
    
    /**
     * 是否启用异步下载，默认true
     */
    private boolean asyncDownloadEnabled = true;
    
    /**
     * 异步下载总超时时间（小时），默认2小时
     */
    private int asyncTotalTimeoutHours = 2;
    
    /**
     * 单个文件下载重试次数，默认3次
     */
    private int retryCount = 3;
    
    /**
     * 重试间隔时间（秒），默认30秒
     */
    private int retryIntervalSeconds = 30;
}
