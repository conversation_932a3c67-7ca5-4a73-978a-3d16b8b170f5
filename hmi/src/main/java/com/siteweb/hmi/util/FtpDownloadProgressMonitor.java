package com.siteweb.hmi.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.io.CopyStreamAdapter;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.atomic.AtomicLong;

/**
 * FTP下载进度监控工具
 * 
 * <AUTHOR>
 * @since 2025/7/31
 */
@Slf4j
public class FtpDownloadProgressMonitor {
    
    private final String fileName;
    private final long fileSize;
    private final AtomicLong bytesTransferred = new AtomicLong(0);
    private long lastLogTime = System.currentTimeMillis();
    private long lastLogBytes = 0;
    
    public FtpDownloadProgressMonitor(String fileName, long fileSize) {
        this.fileName = fileName;
        this.fileSize = fileSize;
    }
    
    /**
     * 创建带进度监控的FTP客户端
     */
    public static FTPClient createMonitoredFtpClient(String fileName, long fileSize) {
        FTPClient ftpClient = new FTPClient();
        FtpDownloadProgressMonitor monitor = new FtpDownloadProgressMonitor(fileName, fileSize);
        
        ftpClient.setCopyStreamListener(new CopyStreamAdapter() {
            @Override
            public void bytesTransferred(long totalBytesTransferred, int bytesTransferred, long streamSize) {
                monitor.updateProgress(totalBytesTransferred);
            }
        });
        
        return ftpClient;
    }
    
    /**
     * 更新下载进度
     */
    public void updateProgress(long totalBytesTransferred) {
        bytesTransferred.set(totalBytesTransferred);
        
        long currentTime = System.currentTimeMillis();
        // 每10秒记录一次进度
        if (currentTime - lastLogTime >= 10000) {
            logProgress(currentTime);
            lastLogTime = currentTime;
            lastLogBytes = totalBytesTransferred;
        }
    }
    
    /**
     * 记录下载进度
     */
    private void logProgress(long currentTime) {
        long currentBytes = bytesTransferred.get();
        double progressPercent = fileSize > 0 ? (double) currentBytes / fileSize * 100 : 0;
        
        // 计算下载速度
        long timeDiff = currentTime - lastLogTime;
        long bytesDiff = currentBytes - lastLogBytes;
        double speedKBps = timeDiff > 0 ? (double) bytesDiff / timeDiff : 0; // KB/s
        
        // 估算剩余时间
        String remainingTime = "未知";
        if (speedKBps > 0 && fileSize > currentBytes) {
            long remainingBytes = fileSize - currentBytes;
            long remainingSeconds = (long) (remainingBytes / speedKBps / 1000);
            remainingTime = formatTime(remainingSeconds);
        }
        
        log.info("下载进度 [{}]: {:.1f}% ({}/{} bytes), 速度: {:.1f} KB/s, 预计剩余时间: {}", 
                fileName, progressPercent, currentBytes, fileSize, speedKBps, remainingTime);
    }
    
    /**
     * 格式化时间显示
     */
    private String formatTime(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + minutes + "分钟";
        }
    }
    
    /**
     * 下载完成时调用
     */
    public void downloadCompleted() {
        long totalTime = System.currentTimeMillis() - (lastLogTime - 10000); // 粗略计算总时间
        double avgSpeedKBps = totalTime > 0 ? (double) bytesTransferred.get() / totalTime : 0;
        
        log.info("文件下载完成 [{}]: {} bytes, 平均速度: {:.1f} KB/s, 总耗时: {}", 
                fileName, bytesTransferred.get(), avgSpeedKBps, formatTime(totalTime / 1000));
    }
    
    /**
     * 下载失败时调用
     */
    public void downloadFailed(Exception e) {
        log.error("文件下载失败 [{}]: 已下载 {} bytes, 错误: {}", 
                fileName, bytesTransferred.get(), e.getMessage());
    }
    
    /**
     * 获取当前下载进度百分比
     */
    public double getProgressPercent() {
        return fileSize > 0 ? (double) bytesTransferred.get() / fileSize * 100 : 0;
    }
    
    /**
     * 获取已下载字节数
     */
    public long getBytesTransferred() {
        return bytesTransferred.get();
    }
}
