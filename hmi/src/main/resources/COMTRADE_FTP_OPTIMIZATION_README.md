# COMTRADE FTP下载优化方案

## 问题描述
原有的FTP下载实现存在以下问题：
1. 数据传输超时时间过短（60秒），无法适应慢速网络
2. 同步阻塞执行，一个设备下载慢会影响其他设备
3. 缺乏重试机制和进度监控
4. 无法灵活配置超时参数

## 解决方案

### 1. 增强的超时配置
- **连接超时**: 30秒（可配置）
- **Socket超时**: 5分钟（可配置）
- **数据传输超时**: 60分钟（可配置，针对慢速网络）

### 2. 异步并行下载
- 支持多设备并行下载，避免相互阻塞
- 可配置启用/禁用异步模式
- 总体超时控制，防止无限等待

### 3. 重试机制
- 可配置重试次数（默认3次）
- 可配置重试间隔（默认30秒）
- 智能重试，避免网络拥塞

### 4. 进度监控
- 实时显示下载进度
- 计算下载速度和预计剩余时间
- 详细的日志记录

## 新增文件

### 1. ComtradeFtpConfig.java
配置类，管理所有FTP下载相关的参数：
```java
@ConfigurationProperties(prefix = "comtrade.ftp")
public class ComtradeFtpConfig {
    private int connectTimeoutMs = 30000;
    private int socketTimeoutMs = 300000;
    private int dataTimeoutMinutes = 60;
    private boolean asyncDownloadEnabled = true;
    // ... 其他配置
}
```

### 2. FtpDownloadProgressMonitor.java
下载进度监控工具，提供：
- 实时进度显示
- 下载速度计算
- 剩余时间估算

### 3. comtrade-ftp-config-example.yml
配置示例文件，包含不同网络环境的推荐配置。

## 修改的文件

### 1. FtpClient.java
- 添加可配置的超时时间支持
- 新增带超时参数的构造函数
- 增强的超时配置方法

### 2. ComtradeFtpService.java & ComtradeFtpServiceImpl.java
- 新增带超时控制的下载方法
- 重构内部下载逻辑
- 更长的默认超时时间

### 3. ComtradeFTPRunner.java
- 支持异步并行下载
- 集成重试机制
- 使用配置化的超时参数

## 配置说明

在 `application.yml` 中添加以下配置：

```yaml
comtrade:
  ftp:
    # 基础超时配置
    connect-timeout-ms: 30000        # 连接超时30秒
    socket-timeout-ms: 300000        # Socket超时5分钟
    data-timeout-minutes: 60         # 数据传输超时60分钟
    
    # 异步下载配置
    async-download-enabled: true     # 启用异步下载
    async-total-timeout-hours: 2     # 总超时2小时
    
    # 重试配置
    retry-count: 3                   # 重试3次
    retry-interval-seconds: 30       # 重试间隔30秒
```

### 不同网络环境的推荐配置

#### 快速网络环境
```yaml
comtrade:
  ftp:
    data-timeout-minutes: 30
    async-total-timeout-hours: 1
    retry-count: 2
```

#### 慢速网络环境
```yaml
comtrade:
  ftp:
    socket-timeout-ms: 600000        # 10分钟
    data-timeout-minutes: 120        # 2小时
    async-total-timeout-hours: 4     # 4小时
    retry-count: 5
    retry-interval-seconds: 60
```

#### 极慢网络环境
```yaml
comtrade:
  ftp:
    socket-timeout-ms: 1800000       # 30分钟
    data-timeout-minutes: 240        # 4小时
    async-download-enabled: false    # 禁用并行下载
    async-total-timeout-hours: 8     # 8小时
    retry-interval-seconds: 120      # 2分钟重试间隔
```

## 使用方法

### 1. 自动配置
系统启动时会自动读取配置并应用到FTP下载过程中。

### 2. 手动调用带超时的下载方法
```java
@Autowired
private ComtradeFtpService ftpService;

// 使用自定义超时时间下载（120分钟）
int count = ftpService.downloadLatestComtradeFilesWithTimeout(
    ipAddress, equipmentId, 120);
```

### 3. 监控下载进度
查看应用日志，会显示详细的下载进度信息：
```
下载进度 [file.dat]: 45.2% (1398720/3145728 bytes), 速度: 12.5 KB/s, 预计剩余时间: 2分30秒
```

## 优势

1. **适应慢速网络**: 大幅增加超时时间，支持慢速网络环境
2. **提高并发性**: 异步下载避免设备间相互阻塞
3. **增强稳定性**: 重试机制提高下载成功率
4. **可观测性**: 详细的进度监控和日志记录
5. **灵活配置**: 支持不同网络环境的配置优化
6. **向后兼容**: 保持原有API不变，新功能可选启用

## 注意事项

1. 异步下载会增加系统资源消耗，请根据服务器性能调整
2. 超时时间设置过长可能导致系统响应缓慢
3. 重试次数过多可能加重网络负担
4. 建议根据实际网络环境调整配置参数
