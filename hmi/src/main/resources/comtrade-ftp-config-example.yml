# COMTRADE FTP下载配置示例
# 将以下配置添加到你的application.yml文件中

comtrade:
  ftp:
    # 连接超时时间（毫秒），默认30秒
    connect-timeout-ms: 30000
    
    # Socket超时时间（毫秒），默认5分钟
    # 对于慢速网络，可以适当增加这个值
    socket-timeout-ms: 300000
    
    # 数据传输超时时间（分钟），默认60分钟
    # 这是单个文件下载的最大允许时间
    # 对于3MB文件在慢速网络下，建议设置为60-120分钟
    data-timeout-minutes: 60
    
    # 是否启用异步下载，默认true
    # true: 多个设备并行下载，不会互相阻塞
    # false: 串行下载，一个设备完成后再下载下一个
    async-download-enabled: true
    
    # 异步下载总超时时间（小时），默认2小时
    # 所有设备下载任务的总超时时间
    async-total-timeout-hours: 2
    
    # 单个文件下载重试次数，默认3次
    retry-count: 3
    
    # 重试间隔时间（秒），默认30秒
    retry-interval-seconds: 30

# 针对不同网络环境的推荐配置：

# 1. 快速网络环境（推荐配置）：
# comtrade:
#   ftp:
#     connect-timeout-ms: 30000
#     socket-timeout-ms: 120000      # 2分钟
#     data-timeout-minutes: 30       # 30分钟
#     async-download-enabled: true
#     async-total-timeout-hours: 1
#     retry-count: 2
#     retry-interval-seconds: 15

# 2. 慢速网络环境（推荐配置）：
# comtrade:
#   ftp:
#     connect-timeout-ms: 60000      # 1分钟连接超时
#     socket-timeout-ms: 600000      # 10分钟Socket超时
#     data-timeout-minutes: 120      # 2小时数据传输超时
#     async-download-enabled: true
#     async-total-timeout-hours: 4   # 4小时总超时
#     retry-count: 5                 # 增加重试次数
#     retry-interval-seconds: 60     # 1分钟重试间隔

# 3. 极慢网络环境（推荐配置）：
# comtrade:
#   ftp:
#     connect-timeout-ms: 120000     # 2分钟连接超时
#     socket-timeout-ms: 1800000     # 30分钟Socket超时
#     data-timeout-minutes: 240      # 4小时数据传输超时
#     async-download-enabled: false  # 使用串行下载避免网络拥塞
#     async-total-timeout-hours: 8   # 8小时总超时
#     retry-count: 3
#     retry-interval-seconds: 120    # 2分钟重试间隔
