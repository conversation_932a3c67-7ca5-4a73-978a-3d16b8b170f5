<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.complexindex.mapper.ComplexIndexBusinessTypeMapper">

    <resultMap type="com.siteweb.complexindex.entity.ComplexIndexBusinessType" id="ComplexindexbusinesstypeMap">
        <result property="businessTypeId" column="BusinessTypeId" jdbcType="INTEGER"/>
        <result property="businessTypeName" column="BusinessTypeName" jdbcType="VARCHAR"/>
        <result property="parentId" column="ParentId" jdbcType="INTEGER"/>
        <result property="description" column="Description" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

